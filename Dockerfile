# Usa PHP 8.1 con FPM
FROM php:8.1-fpm

# Instala Nginx y dependencias necesarias
RUN apt-get update && apt-get install -y \
    nginx \
    curl \
    libzip-dev \
    unzip \
    && rm -rf /var/lib/apt/lists/*

# Configura la zona horaria
RUN echo "date.timezone=America/Santiago" > /usr/local/etc/php/conf.d/timezone.ini

# Instala extensiones de PHP (removemos mysql ya que no existe en PHP 7+)
RUN docker-php-ext-install mysqli pdo pdo_mysql zip

# Copia archivo php.ini personalizado
COPY php.ini /usr/local/etc/php/php.ini

# Elimina la configuración por defecto de Nginx
RUN rm /etc/nginx/sites-enabled/default

# Copia la configuración de Nginx para la aplicación
COPY nginx.conf /etc/nginx/sites-available/default
RUN ln -s /etc/nginx/sites-available/default /etc/nginx/sites-enabled/default

# Copia el código fuente
COPY ENTERPRISE_CL_v417/ /var/www/html/

# Copia el script de inicio
COPY start.sh /start.sh
RUN chmod +x /start.sh

# Ajusta permisos
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html

# Expone el puerto 80 para Nginx
EXPOSE 80

# Inicia los servicios
CMD ["/start.sh"]
