<?php
/**
 * Script de prueba para validar la migración de PHP 5.6 a PHP 8.1
 * Este script verifica que las funciones de compatibilidad funcionen correctamente
 */

echo "=== PRUEBA DE MIGRACIÓN PHP 5.6 → PHP 8.1 ===\n\n";

// Verificar versión de PHP
echo "1. Verificando versión de PHP:\n";
echo "   Versión actual: " . PHP_VERSION . "\n";
echo "   Versión mínima requerida: 8.1.0\n";

if (version_compare(PHP_VERSION, '8.1.0', '>=')) {
    echo "   ✅ Versión de PHP compatible\n\n";
} else {
    echo "   ❌ Versión de PHP no compatible\n\n";
    exit(1);
}

// Verificar extensiones requeridas
echo "2. Verificando extensiones de PHP:\n";
$required_extensions = ['mysqli', 'pdo', 'pdo_mysql'];

foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "   ✅ Extensión $ext: disponible\n";
    } else {
        echo "   ❌ Extensión $ext: NO disponible\n";
    }
}
echo "\n";

// Verificar que las funciones mysql_* ya no existen
echo "3. Verificando que funciones mysql_* deprecadas no existen:\n";
$deprecated_functions = [
    'mysql_connect',
    'mysql_query', 
    'mysql_fetch_array',
    'mysql_insert_id',
    'mysql_affected_rows'
];

foreach ($deprecated_functions as $func) {
    if (function_exists($func)) {
        echo "   ⚠️  Función $func: aún existe (usando wrapper de compatibilidad)\n";
    } else {
        echo "   ✅ Función $func: correctamente removida\n";
    }
}
echo "\n";

// Probar conexión con configuración del proyecto
echo "4. Probando conexión a base de datos:\n";

// Incluir configuración del proyecto
if (file_exists('ENTERPRISE_CL_v417/config.php')) {
    include 'ENTERPRISE_CL_v417/config.php';
    echo "   ✅ Archivo de configuración cargado\n";
    
    // Probar conexión MySQLi
    echo "   Probando conexión MySQLi...\n";
    $mysqli = @mysqli_connect(SERVERDB, USERDB, PASSDB, DB);
    
    if ($mysqli) {
        echo "   ✅ Conexión MySQLi exitosa\n";
        mysqli_close($mysqli);
    } else {
        echo "   ❌ Error en conexión MySQLi: " . mysqli_connect_error() . "\n";
    }
    
    // Probar conexión PDO
    echo "   Probando conexión PDO...\n";
    try {
        $pdo = new PDO("mysql:dbname=".DB.";host=".SERVERDB, USERDB, PASSDB);
        echo "   ✅ Conexión PDO exitosa\n";
        $pdo = null;
    } catch (PDOException $e) {
        echo "   ❌ Error en conexión PDO: " . $e->getMessage() . "\n";
    }
} else {
    echo "   ⚠️  Archivo de configuración no encontrado\n";
}
echo "\n";

// Probar funciones de compatibilidad
echo "5. Probando funciones de compatibilidad:\n";

if (file_exists('ENTERPRISE_CL_v417/includes/php/mysql_compatibility.php')) {
    include 'ENTERPRISE_CL_v417/includes/php/mysql_compatibility.php';
    echo "   ✅ Archivo de compatibilidad cargado\n";
    
    // Verificar que las funciones wrapper existen
    $wrapper_functions = [
        'mysql_query',
        'mysql_fetch_array', 
        'mysql_insert_id',
        'mysql_affected_rows'
    ];
    
    foreach ($wrapper_functions as $func) {
        if (function_exists($func)) {
            echo "   ✅ Función wrapper $func: disponible\n";
        } else {
            echo "   ❌ Función wrapper $func: NO disponible\n";
        }
    }
} else {
    echo "   ❌ Archivo de compatibilidad no encontrado\n";
}
echo "\n";

// Verificar sintaxis de archivos PHP principales
echo "6. Verificando sintaxis de archivos principales:\n";

$main_files = [
    'ENTERPRISE_CL_v417/config.php',
    'ENTERPRISE_CL_v417/dbConn.php',
    'ENTERPRISE_CL_v417/index.php',
    'ENTERPRISE_CL_v417/includes/php/general_funcs.php'
];

foreach ($main_files as $file) {
    if (file_exists($file)) {
        $output = [];
        $return_code = 0;
        exec("php -l $file 2>&1", $output, $return_code);
        
        if ($return_code === 0) {
            echo "   ✅ $file: sintaxis correcta\n";
        } else {
            echo "   ❌ $file: errores de sintaxis\n";
            echo "      " . implode("\n      ", $output) . "\n";
        }
    } else {
        echo "   ⚠️  $file: archivo no encontrado\n";
    }
}
echo "\n";

echo "=== RESUMEN DE LA MIGRACIÓN ===\n";
echo "La migración de PHP 5.6 a PHP 8.1 incluye:\n";
echo "✅ Actualización del Dockerfile a PHP 8.1\n";
echo "✅ Actualización de php.ini para PHP 8.1\n";
echo "✅ Creación de funciones de compatibilidad mysql_*\n";
echo "✅ Reemplazo de split() por explode()\n";
echo "✅ Configuración de conexiones MySQLi y PDO\n\n";

echo "PRÓXIMOS PASOS RECOMENDADOS:\n";
echo "1. Ejecutar: docker-compose build\n";
echo "2. Ejecutar: docker-compose up\n";
echo "3. Probar funcionalidades principales de la aplicación\n";
echo "4. Migrar gradualmente de mysql_* a mysqli_* o PDO\n";
echo "5. Actualizar código para aprovechar características de PHP 8.1\n\n";

echo "=== FIN DE LA PRUEBA ===\n";
?>
