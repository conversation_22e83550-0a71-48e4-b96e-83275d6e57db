<?php
/**
 * Funciones de compatibilidad para migrar de mysql_* a mysqli_*
 * Este archivo proporciona funciones wrapper para facilitar la migración
 */

// Variable global para la conexión MySQLi
global $mysqli_connection;

/**
 * Wrapper para mysql_query()
 */
function mysql_query($query, $connection = null) {
    global $mysqli_connection;
    $conn = $connection ?: $mysqli_connection;
    
    if (!$conn) {
        trigger_error("No hay conexión MySQLi disponible", E_USER_ERROR);
        return false;
    }
    
    $result = mysqli_query($conn, $query);
    if (!$result && mysqli_error($conn)) {
        trigger_error("MySQL Error: " . mysqli_error($conn), E_USER_WARNING);
    }
    
    return $result;
}

/**
 * Wrapper para mysql_fetch_array()
 */
function mysql_fetch_array($result, $result_type = MYSQLI_BOTH) {
    if (!$result) return false;
    return mysqli_fetch_array($result, $result_type);
}

/**
 * Wrapper para mysql_fetch_assoc()
 */
function mysql_fetch_assoc($result) {
    if (!$result) return false;
    return mysqli_fetch_assoc($result);
}

/**
 * Wrapper para mysql_fetch_row()
 */
function mysql_fetch_row($result) {
    if (!$result) return false;
    return mysqli_fetch_row($result);
}

/**
 * Wrapper para mysql_num_rows()
 */
function mysql_num_rows($result) {
    if (!$result) return false;
    return mysqli_num_rows($result);
}

/**
 * Wrapper para mysql_insert_id()
 */
function mysql_insert_id($connection = null) {
    global $mysqli_connection;
    $conn = $connection ?: $mysqli_connection;
    
    if (!$conn) return false;
    return mysqli_insert_id($conn);
}

/**
 * Wrapper para mysql_affected_rows()
 */
function mysql_affected_rows($connection = null) {
    global $mysqli_connection;
    $conn = $connection ?: $mysqli_connection;
    
    if (!$conn) return false;
    return mysqli_affected_rows($conn);
}

/**
 * Wrapper para mysql_error()
 */
function mysql_error($connection = null) {
    global $mysqli_connection;
    $conn = $connection ?: $mysqli_connection;
    
    if (!$conn) return "No connection";
    return mysqli_error($conn);
}

/**
 * Wrapper para mysql_errno()
 */
function mysql_errno($connection = null) {
    global $mysqli_connection;
    $conn = $connection ?: $mysqli_connection;
    
    if (!$conn) return 0;
    return mysqli_errno($conn);
}

/**
 * Wrapper para mysql_connect() - DEPRECADO, usar mysqli_connect()
 */
function mysql_connect($server, $username, $password) {
    trigger_error("mysql_connect() está deprecado, usar mysqli_connect()", E_USER_DEPRECATED);
    return mysqli_connect($server, $username, $password);
}

/**
 * Wrapper para mysql_select_db() - DEPRECADO
 */
function mysql_select_db($database, $connection = null) {
    global $mysqli_connection;
    $conn = $connection ?: $mysqli_connection;
    
    if (!$conn) return false;
    trigger_error("mysql_select_db() está deprecado, especificar DB en mysqli_connect()", E_USER_DEPRECATED);
    return mysqli_select_db($conn, $database);
}

/**
 * Wrapper para mysql_set_charset()
 */
function mysql_set_charset($charset, $connection = null) {
    global $mysqli_connection;
    $conn = $connection ?: $mysqli_connection;
    
    if (!$conn) return false;
    return mysqli_set_charset($conn, $charset);
}

/**
 * Wrapper para mysql_real_escape_string()
 */
function mysql_real_escape_string($string, $connection = null) {
    global $mysqli_connection;
    $conn = $connection ?: $mysqli_connection;
    
    if (!$conn) return addslashes($string); // Fallback básico
    return mysqli_real_escape_string($conn, $string);
}

/**
 * Wrapper para mysql_free_result()
 */
function mysql_free_result($result) {
    if (!$result) return false;
    return mysqli_free_result($result);
}

/**
 * Función helper para escapar strings de forma segura
 */
function escape_string($string, $connection = null) {
    global $mysqli_connection;
    $conn = $connection ?: $mysqli_connection;
    
    if (!$conn) return addslashes($string);
    return mysqli_real_escape_string($conn, $string);
}

/**
 * Función helper para ejecutar queries preparadas de forma simple
 */
function execute_prepared_query($query, $params = [], $types = '') {
    global $mysqli_connection;
    
    if (!$mysqli_connection) return false;
    
    $stmt = mysqli_prepare($mysqli_connection, $query);
    if (!$stmt) return false;
    
    if (!empty($params)) {
        if (empty($types)) {
            // Auto-detectar tipos
            $types = str_repeat('s', count($params)); // Por defecto string
        }
        mysqli_stmt_bind_param($stmt, $types, ...$params);
    }
    
    $result = mysqli_stmt_execute($stmt);
    
    if ($result) {
        $result = mysqli_stmt_get_result($stmt);
    }
    
    mysqli_stmt_close($stmt);
    return $result;
}
?>
