<?php
include ('dbConn.php');
?>

<?php

function sendNow() {

    title("Env&iacute;o de SMS", "Individual Inmediato", "send");
    $login = $_SESSION["user"];
    $max_char = return_max_char();
    ?>
    <form name="tmc" action="#">
        <table width="100%" border="0" cellpadding="0" cellspacing="2" class="table-general">
            <tr>
                <td align="left"width="120"><b>Celular</b></td>
                <td align="left"><?php input_phone(''); ?></td>
            </tr>
            <tr>
                <td align="left" valign="top"><b>Mensaje</b></td>
                <td align="left"> 
                    <textarea cols="50" rows="7"  name="msgtext" wrap="hard" placeholder="Escriba Su Mensaje:"  onKeyDown="contador(this.form.msgtext, this.form.remLen,<?= $max_char ?>);" onKeyUp="contador(this.form.msgtext, this.form.remLen,<?= $max_char ?>);"></textarea>
                </td>
            </tr>
            <tr>
                <td align="left"></td>
                <td align="left"><input type="text" name="remLen" size="3" value="<?= $max_char ?>"  readonly> (Caracteres Disponibles)</td>
            </tr>
            <tr>
                <td></td>
                <td align="left" height="30" valign="bottom">
                    <span class="buttom" onClick="sendNow();"/>ENVIAR</span>
                </td>
            </tr>
            <tr>
                <td></td>
                <td align="left" height="50" valign="bottom">
                    <br />
                    <br />
                    <div id="resultado"></div>
                </td>
            </tr>
        </table>
    </form>
    <?php
}
?>

<?php

function sendAfter() {

    title("Env&iacute;o de SMS", "Individual Agendado", "send");
    $login = $_SESSION["user"];

    $format_date = "Y-m-d H:i:s";
    $sendDateAfter = return_date_now_format($format_date);
    $max_char = return_max_char();
    $date_now = return_date_now_format($format_date);
    ?>
    <form name="tmc" action="">
        <table width="100%" border="0" cellpadding="0" cellspacing="2" class="table-general">
            <tr>
                <td align="left" valign="middle" width="120"><b>Celular</b></td>
                <td align="left" valign="middle" colspan="2"><?php input_phone(''); ?></td>
            </tr>
            <tr>
                <td align="left" valign="middle" height="20"><b>Fecha de Despacho</b></td>
    <!--						<td align="left" valign="middle" width="100"><input type="text" id="sendDateAfter" name="date" value="< ?= date('Y'); ?>-< ?= date('m'); ?>-< ?= date('d'); ?> < ?= $sendDateAfter ?>:< ?= date('i'); ?>" readonly> </td>-->
                <td align="left" valign="middle" width="100">
                    <input type="text" id="sendDateAfter" name="date" value="<?= $sendDateAfter ?>" readonly> 
                </td>
                <td align="left"  width="530">
                    <img src="includes/javascript/images/calendar.png" id="lanzador">
                </td>
            </tr>
            <tr>
                <td align="left" valign="top"><b>Mensaje</b></td>
                <td align="left" valign="top" colspan="2"> 
                    <textarea cols="50" rows="7"  name="msgtext" wrap="hard" placeholder="Escriba Su Mensaje:" onKeyDown="contador(this.form.msgtext, this.form.remLen,<?= $max_char ?>);" onKeyUp="contador(this.form.msgtext, this.form.remLen,<?= $max_char ?>);"></textarea></td>
            </tr>
            <tr>
                <td align="left" valign="top"></td>
                <td align="left" valign="top" colspan="2"><input type="text" name="remLen" size="3" value="<?= $max_char ?>"  readonly> (Caracteres Disponibles)</td>
            </tr>
            <tr>
                <td></td>
                <td align="left" valign="bottom" height="30">
                    <span onClick="sendAfter('<?= $date_now ?>');" class="buttom">ENVIAR</span>  
                    <span onClick="reset();" class="buttom">Borrar</span>
                </td>
            </tr>
            <tr>
                <td></td>
                <td align="left" valign="bottom" height="30" colspan="2"><br /><br /><div id="resultado"></div></td>
            </tr>
        </table>
    </form>
    <script type="text/javascript">
        var fecha = new Date();
        var year = fecha.getFullYear();
        Calendar.setup({inputField: "sendDateAfter", range: [year, 2999],
            ifFormat: "%Y-%m-%d %H:%M",
            showsTime: true,
            timeFormat: "24",
            button: "lanzador"
        })
    </script>

    <?php
}
?>



<?php

function sendNowGroup() {

    title("Env&iacute;o de SMS", "Grupal Inmediato", "send");
    $login = $_SESSION["user"];
    $max_char = return_max_char();
    $max_upload = 0;
    if (defined("MAX_ROW_MSG_UPLOAD")) {
        $max_upload = MAX_ROW_MSG_UPLOAD;
    }
    ?>
    <input id="txtMaxUpload" type="hidden" value="<?= $max_upload ?>" />
    <script type="text/javascript" src="jquery-3.7.1.min.js"></script>
    <script type="text/javascript">
        $(document).ready(function () {
            $('#idGroup').change(function () {
                var texto = $('#idGroup option:selected').text();
                texto = texto.split("(")[1].replace(")", "");
                maxUpload = $('#txtMaxUpload').val();
                if (parseInt(texto) > parseInt(maxUpload)) {
                    $('#idGroup').val('');
                    alert('El grupo exede la cantidad máxima de carga de ' + maxUpload + ' abonados.');
                }
            });
        });
    </script>
    <form name="tmc" action="">
        <table width="100%" border="0" cellpadding="0" cellspacing="2" class="table-general">
            <tr>
                <td align="left" valign="top" width="120"><b>Grupo</b></td>
                <td align="left" valign="top"><?php select_groups(''); ?></td>
            </tr>
            <tr>
                <td align="left" valign="top"><b>Mensaje</b></td>
                <td align="left" valign="top"> 
                    <textarea cols="50" rows="7"  name="msgtext" placeholder="Escriba Su Mensaje:" wrap="hard" onKeyDown="contador(this.form.msgtext, this.form.remLen,<?= $max_char ?>);" onKeyUp="contador(this.form.msgtext, this.form.remLen,<?= $max_char ?>);"></textarea></td>
            </tr>
            <tr>
                <td align="left" valign="top"></td>
                <td align="left" valign="top"><input type="text" name="remLen" size="3" value="<?= $max_char ?>"  readonly> (Caracteres Disponibles)<br /><br /></td>
            </tr>
            <tr>
                <td></td>
                <td align="left" valign="buttom" height="30"><span onClick="sendNowGroup();" class="buttom">ENVIAR</span>  <span onClick="reset();" class="buttom">Borrar</span></td>
            </tr>
            <tr>
                <td></td>
                <td align="left" height="30" valign="bottom"><br /><br /><div id="resultado"></div></td>
            </tr>
        </table>
    </form>
    <?php
}
?>

<?php

function sendAfterGroup() {

    title("Env&iacute;o de SMS", "Grupal Agendado", "send");
    $login = $_SESSION["user"];
    $format = "Y-m-d H:i";
    $sendDateAfter = return_date_now_format($format);
    $max_char = return_max_char();
    $date_now = return_date_now();
    $max_upload = 0;
    if (defined("MAX_ROW_MSG_UPLOAD")) {
        $max_upload = MAX_ROW_MSG_UPLOAD;
    }
    ?>
    <input id="txtMaxUpload" type="hidden" value="<?= $max_upload ?>" />
    <script type="text/javascript" src="jquery-3.7.1.min.js"></script>
    <script type="text/javascript">
                    $(document).ready(function () {
                        $('#idGroup').change(function () {
                            var texto = $('#idGroup option:selected').text();
                            texto = texto.split("(")[1].replace(")", "");
                            maxUpload = $('#txtMaxUpload').val();
                            //						console.log("text: "+texto+" max: "+maxUpload)
                            if (parseInt(texto) > parseInt(maxUpload)) {
                                $('#idGroup').val('');
                                alert('El grupo exede la cantidad máxima de carga de ' + maxUpload + ' abonados.');
                            }
                        });
                    });
    </script>
    <form name="tmc" action="">
        <table width="100%" border="0" cellpadding="0" cellspacing="2" class="table-general">
            <tr>
                <td align="left" valign="top" width="120"><b>Grupo</b></td>
                <td align="left" valign="top" colspan="2"><?php select_groups(''); ?></td>
            </tr>
            <tr>
                <td align="left" valign="middle" height="20"><b>Fecha de Despacho</b></td>
                <td align="left" valign="middle" width="100"><input type="text" id="sendDateAfter" value="<?= $sendDateAfter ?>" name="sendDateAfter" readonly> </td>
                <td align="left"  width="530"><img src="includes/javascript/images/calendar.png" id="lanzador"></td>
            </tr>
            <tr>
                <td align="left" valign="top"><b>Mensaje</b></td>
                <td align="left" valign="top" colspan="2"> 
                    <textarea cols="50" rows="7" name="msgtext" placeholder="Escriba Su Mensaje:" wrap="hard" onKeyDown="contador(this.form.msgtext, this.form.remLen,<?= $max_char ?>);" onKeyUp="contador(this.form.msgtext, this.form.remLen,<?= $max_char ?>);"></textarea>
                </td>
            </tr>
            <tr>
                <td align="left" valign="top"></td>
                <td align="left" valign="top" colspan="2"><input type="text" name="remLen" size="3" value="<?= $max_char ?>"  readonly> (Caracteres Disponibles)</td>
            </tr>
            <tr>
                <td></td>
                <td align="left" valign="bottom" height="30" colspan="2">
                    <span onClick="sendAfterGroup('<?= $date_now ?>');" class="buttom">ENVIAR</span>
                    <span onClick="reset();" class="buttom">Borrar</span>
                </td>
            </tr>
            <tr>
                <td></td>
                <td align="left" height="50" valign="bottom" colspan="2"><br /><br /><div id="resultado"></div></td>
            </tr>
        </table>
    </form>
    <script type="text/javascript">
        var fecha = new Date();
        var year = fecha.getFullYear();
        Calendar.setup({inputField: "sendDateAfter", range: [year, 2999],
            ifFormat: "%Y-%m-%d %H:%M",
            showsTime: true,
            timeFormat: "24",
            button: "lanzador"
        }
        )
    </script>
    <?php
}
?>

<?php

function sendFile() {

    title("Env&iacute;o de SMS", "Desde archivo.", "send");
    $login = $_SESSION["user"];
    $company = $_SESSION["id"];
    ?>
    <form action="?seccion=sendFileValue" method="POST" name="tmc" accept-charset="UTF-8" enctype="multipart/form-data">
        <table width="100%" border="0" cellpadding="0" cellspacing="2" class="table-general">
            <tr>
                <td align="left" valign="top" height="30">Tipo de Archivo</td>
                <td align="left" valign="top"><b>TXT</b> o <b>CSV</b></td>
            </tr>
            <tr>
                <td align="left" valign="top" height="30">Formato de Archivo</td>
                <td align="left" valign="top">
                    Celular;Fecha;Mensaje (La fecha s&oacute;lo se ingresa en caso de ser un env&iacute;o agendado)
                </td>
            </tr>
            <tr>
                <td align="left" valign="top" height="30">Archivo </td>
                <td align="left" valign="top">
                    <input type="file" name="filetxt" >
                    <input type="submit" value="CARGAR" >
                </td>
            </tr>
        </table>
    </form>
    <div id="resultado"></div>
    <?php
}
?>

<?php

function sendFileValue() {
    title("Env&iacute;o de SMS", "Desde archivo.", "send");
    $val = "...";
    $misdn = $_SESSION["PLACEHOLDER_NUMBER_MOBILE"];
    $lengh = $_SESSION["SIZE_NUMBER_MOBILE"];
    $codePais = $_SESSION["REX_TYPE_NUMBER_MOBILE"];
    $max_char = return_max_char();
    $filename = $_FILES['filetxt']['name'];
    $path = "loadFiles/";
    $fileType = pathinfo($filename);
    $format = "d-m-Y H:i:s";
    $format_date_file = "dmY_His";
    $maximumRecordsPerInsert = 100;
    if (defined("MAX_STACK_MSG_UPLOAD")) {
        $maximumRecordsPerInsert = MAX_STACK_MSG_UPLOAD;
    }
    $date_now = return_date_now_format("Y-m-d H:i:s");
    $name_file = str_replace(" ", "_", substr($filename, 0, -4));
    $date_local_file = return_date_now_format($format_date_file);
    $file_extension = $fileType['extension'];
    $fecha_actual = return_date_now_format($format);

//	$id_file_tmp = insertTempFile($name_file, $fecha_actual);
    $id_process = add_process_uploadfile("UPLOAD", $name_file, 0, $date_now);
//	$filename_tmp = substr($filename, 0, -4) . "_local_" . return_date_now_format($format_date_file) . "_gmt_" . return_date_now_format($format_date_file) . "." . $fileType['extension'];
    $filename_tmp = $name_file . "_id_" . $id_process . "_" . $date_local_file . "." . $file_extension;

    $max_row_file = 0;
    if (defined("MAX_ROW_MSG_UPLOAD")) {
        $max_row_file = MAX_ROW_MSG_UPLOAD;
    }
    $max_characters = 0;
    if (defined("MAX_LENGTH_TEXT_MSG")) {
        $max_characters_msg = MAX_LENGTH_TEXT_MSG;
    }

    $length_movil = 0;
    if ($lengh) {
        $length_movil = $lengh;
    }

    $format_mobil = "";
    if ($misdn) {
        $format_mobil = $misdn;
    }
    $txt_validate_mobil = "";
    if ($codePais) {
        $txt_validate_mobil = $codePais;
    }

    $cont_errores = 0;
    $data_insert = "";
    $end_insert = 1;
    $start_insert = 1;
    $id = 0;
    ?>
    <table width="700" class="table-general" >
        <tr>
            <td >
                <div class="alert_big">
                    <?php
                    if ($fileType['extension'] == "txt" || $fileType['extension'] == "csv") {
                        if (move_uploaded_file($_FILES['filetxt']['tmp_name'], $path . $filename_tmp)) {
                            $arr = "";
                            $encodingFile = "";
                            $cmd_file_linux = 'file -bi ' . $path . $filename_tmp;
                            $retorno_cmd_linux = shell_exec($cmd_file_linux);
                            $arr = explode(";", $retorno_cmd_linux);
                            $arr = explode("=", $arr[1]);
                            $encodingFile = trim($arr[1]);
                            if ($encodingFile != 'utf-8') {
                                $mv_inut = $path . $filename_tmp;
                                $mv_output = $path . $encodingFile . '_' . $filename_tmp;
                                $cmd_mv_linux = "mv $mv_inut $mv_output";
                                shell_exec($cmd_mv_linux);

                                $file_output = $path . $filename_tmp;
                                $file_input = $path . $encodingFile . '_' . $filename_tmp;
                                $cmd_iconv_linux = "iconv -t utf-8 -f $encodingFile -o $file_output $file_input";
                                shell_exec($cmd_iconv_linux);
                            }

                            $lines = file($path . $filename_tmp);
                            $lengh_array = count($lines);
                            if ($lengh_array <= $max_row_file) {
                                // revision de linea 1
                                $line = $lines[0];
                                $datos = explode(";", $line);
                                $cant_colum = count($datos);
                                if ($cant_colum == 3) {
                                    $msg_preview = trim($datos[2]);
                                    $phone_preview = trim($datos[0]);
                                    if ($id_process > 0) {
                                        $status = "PROCESSING";
                                        updateProcessFile($filename_tmp, $id_process, $lengh_array, "", $status); //esto para editar el nombre del archivo con el id de carga
                                        //iteracion de las lineas del archivo
                                        for ($index = 0; $index < $lengh_array; $index++) {
                                            $carrier = "UNKNOWN";
                                            $line = quitarEnter($lines[$index]);
                                            if (strlen($line) > 0) {
                                                $i = $index + 1;
                                                //se obtienen los datos de la linea del archivo
                                                $datos = explode(";", $line);
                                                $phone = trim($datos[0]);
                                                $date = trim($datos[1]);
                                                $msg = trim($datos[2]);
                                                //validacion de numero mobil
                                                if (validate_format_phone($phone, $txt_validate_mobil)) {
                                                    $carrier = return_carrier($phone);
                                                } else {
                                                    $error_msg = "El numero no cumple con el formato requerido de $length_movil digitos ($format_mobil)";
                                                    echo "<b>Error Line $i:</b> El n&uacute;mero no cumple con el formato requerido de $length_movil digitos ($format_mobil)<br>";
                                                    $cont_errores += 1;
                                                }
                                                if (empty($date)) {
                                                    $date = $fecha_actual;
                                                }
                                                if (validate_format_datetime($date)) {
                                                    $vdate = return_date_after_format($date, $format);
                                                    if (strtotime($vdate) >= strtotime($fecha_actual)) {
                                                        if (!validate_time_available_send_message($date)) {
                                                            $error_msg = "La fecha $date se encuentra fuera de los rangos de disponibilidad del servicio.";
                                                            $msgdate = "<b>Error Line $i:</b> La fecha <b>$date</b> se encuentra fuera de los rangos de disponibilidad del servicio.<br>";
                                                            echo $msgdate;
                                                            $cont_errores += 1;
                                                        }
                                                    } else {
                                                        $error_msg = "La fecha/hora de $date no puede ser menor a la actual";
                                                        echo "<b>Error Line $i:</b> La fecha/hora de <b>$date</b> no puede ser menor a la actual<br>";
                                                        $cont_errores += 1;
                                                    }
                                                } else {
                                                    $error_msg = "La fecha $date no coincide con el formato dd-mm-yyyy HH:mm:ss reuerida para la carga agendada";
                                                    echo "<b>Error Line $i:</b> La fecha <b>$date</b> no coincide con el formato <b>dd-mm-yyyy HH:mm:ss</b> reuerida para la carga agendada<br>";
                                                    $cont_errores += 1;
                                                }

                                                //validacion de mensaje
                                                if (empty($msg)) {
                                                    $error_msg = "Mensaje Vacio.";
                                                    echo "<b>Error Line $i:</b> Mensaje Vacio <br>";
                                                    $cont_errores += 1;
                                                }
                                                if (strlen($msg) > $max_characters_msg) {
                                                    $error_msg = "El largo del mensaje no debe ser superior a los $max_characters_msg caracteres";
                                                    echo "<b>Error Line $i:</b> El largo del mensaje no debe ser superior a los $max_characters_msg caracteres";
                                                    $cont_errores += 1;
                                                }
                                                $date_gmt = return_date_after_format_gmt($date, $format);
                                                if (empty($data_insert)) {
                                                    $data_insert = "($id_process, $phone, '$date', '$date_gmt', '$msg', '$carrier', 'PENDING' )";
                                                } else {
                                                    $data_insert .= ", ($id_process, $phone, '$date', '$date_gmt', '$msg', '$carrier', 'PENDING' )";
                                                }
                                                $fila = $i;
                                                $end_insert = $fila;
                                                if ($fila % $maximumRecordsPerInsert == 0) {
                                                    $id = 0;
                                                    /* insertamos en tabla temporal */
                                                    $id = insertDetailTableTemporal($data_insert);
                                                    if ($id <= 0) {
                                                        $error_msg = "Error al insertar las lineas de la $start_insert a la $end_insert.";
                                                        echo "Error al insertar las lineas de la <b>$start_insert:</b> a la <b>$end_insert:</b>";
                                                        $cont_errores += 1;
                                                    }
                                                    $data_insert = "";
                                                    $start_insert = $end_insert + 1;
                                                }
                                            }
                                        }
                                        $id = 0;
                                        $id = insertDetailTableTemporal($data_insert);
                                        if ($id <= 0) {
                                            $error_msg = "Error al insertar las lineas de la $start_insert a la $end_insert";
                                            echo "Error al insertar las lineas de la <b>$start_insert:</b> a la <b>$end_insert:</b>";
                                            $cont_errores += 1;
                                        }
                                        if ($cont_errores == 0) {
                                            ?>
                                            <b>1) <u>Carga de Archivo:</u> <img src="images/icons/confirm.png" /></b>
                                            <br/><br/>
                                            <b>2) <u>Validaci&oacute;n de Archivo:</u> <img src="images/icons/confirm.png" /></b>
                                            <br/><br/>
                                            <b>3) <u>Enviar Archivo: </u></b> <?= $filename_tmp ?> 
                                            <div id="resultado"><br>
                                                <center>
                                                    <u>Previsualización del Mensaje</u><br/>
                                                    El mensaje: <u><?= $msg_preview; ?></u> 
                                                    ser&aacute; enviado al n&uacute;mero <u><?= $phone_preview; ?></u>: 
                                                </center><br/><br/>
                                                <form name="tmc" action="">
                                                    <input type="hidden" name="filetxt" value="<?= $filename_tmp ?>">
                                                    <input type="hidden" name="idfile" value="<?= $id_process ?>">
                                                    <input type="hidden" name="datelocal" value="<?= $date_now ?>">
                                                    <span onclick="sendFile();" class="buttom">ENVIAR</span>
                                                    <span onclick="javascript:history.back();" class="buttom">VOLVER</span>
                                                </form>
                                            </div>
                                            <?php
                                        } else {
                                            $status = "FAILED";
//							$error_msg = ".";
                                            updateProcessFile($filename_tmp, $id_process, $lengh_array, $error_msg, $status); // se actualiza el mensaje a este nivel ya que antes no existe el proceso ni el registro en la base de datos
					    $result = updateDetailProcessToProcessed($id_process, $status);
					    $ruta = getcwd();
					    $cmd_rm_linux = "rm $ruta/$path*$id_process*";
					    shell_exec($cmd_rm_linux);
                                            ?>
                                            <br />
                                            <img src="images/icons/error.png"/>
                                            Favor validar el archivo y volver a cargar.<br><br>
                                            <span onclick="javascript:history.back();" class="buttom">VOLVER</span>
                                            <?php
                                        }
                                    } else {
                                        $error_msg = "Error al insertar el archivo temporal.";
                                        $status = "FAILED";
                                        updateProcessFile($filename_tmp, $id_process, 0, $error_msg, $status); // se actualiza el mensaje a este nivel ya que antes no existe el proceso ni el registro en la base de datos
					$result = updateDetailProcessToProcessed($id_process, $status);
					$ruta = getcwd();
					$cmd_rm_linux = "rm $ruta/$path*$id_process*";
					shell_exec($cmd_rm_linux);
                                        ?>
                                        <img src="images/icons/error.png"/>
                                        <b>Error:</b> Error al insertar el archivo temporal.<br><br>
                                        <span onclick="javascript:history.back();" class="buttom">VOLVER</span>
                                        <?php
                                    }
                                } else {
                                    $status = "FAILED";
                                    $error_msg = "El archivo no cumple el formato establecido (Celular; Fecha; Mensaje)";
                                    updateProcessFile($filename_tmp, $id_process, 0, $error_msg, $status); // se actualiza el mensaje a este nivel ya que antes no existe el proceso ni el registro en la base de datos
				    $result = updateDetailProcessToProcessed($id_process, $status);
				    $ruta = getcwd();
				    $cmd_rm_linux= "rm $ruta/$path*$id_process*";
				    shell_exec($cmd_rm_linux);
                                    ?>
                                    <img src="images/icons/error.png"/>
                                    <b>Error:</b> El archivo no cumple el formato establecido <b>(Celular; Fecha; Mensaje)</b> Favor verificar.<br><br>
                                    <span onclick="javascript:history.back();" class="buttom">VOLVER</span>
                                    <?php
                                }
                            } else {
                                $status = "FAILED";
                                $error_msg = "El archivo excede el maximo de $max_characters_msg lineas.";
                                updateProcessFile($filename_tmp, $id_process, 0, $error_msg, $status); // se actualiza el mensaje a este nivel ya que antes no existe el proceso ni el registro en la base de datos
				$result = updateDetailProcessToProcessed($id_process, $status);
				$ruta = getcwd();
				$cmd_rm_linux ="rm $ruta/$path*$id_process*";
				shell_exec($cmd_rm_linux);
                                ?>
                                <img src="images/icons/error.png"/>
                                <b>Error:</b> El archivo excede el m&aacute;ximo de <?= $max_characters_msg ?> lineas.<br><br>
                                <span onclick="javascript:history.back();" class="buttom">VOLVER</span>
                                <?php
                            }
                        } else {
                            $status = "FAILED";
                            $error_msg = "Ocurrio; algun error al subir el fichero.";
                            updateProcessFile($filename_tmp, $id_process, 0, $error_msg, $status); // se actualiza el mensaje a este nivel ya que antes no existe el proceso ni el registro en la base de datos
			    $result = updateDetailProcessToProcessed($id_process, $status);
			    $ruta = getcwd();
			    $cmd_rm_linux = "rm $ruta/$path*$id_process*";
			    shell_exec($cmd_rm_linux);
                            ?>
                            <img src="images/icons/error.png"/>
                            1) <u>Carga de Archivo:</u> <b>Error:</b>Ocurri&oacute; alg&uacute;n error al subir el fichero.
                            <span onclick="javascript:history.back();" class="buttom">VOLVER</span>
                            <?php
                        }
                    } else {
                        $status = "FAILED";
                        updateProcessFile($filename_tmp, $id_process, 0, $error_msg, $status); // se actualiza el mensaje a este nivel ya que antes no existe el proceso ni el registro en la base de datos
			$result = updateDetailProcessToProcessed($id_process, $status);
			$ruta = getcwd();
			$cmd_rm_linux = "rm $ruta/$path*$id_process*";
			shell_exec($cmd_rm_linux);
                        ?>
                        <img src="images/icons/error.png"/>
                        <b>1) <u>Carga de Archivo:</u></b><br/><br/> <b>Error:</b> El archivo debe ser <b>.txt</b> o <b>.csv</b>. Favor verificar y volver a cargar <a href='?seccion=sendFile'><b>aquí</b></a>.
                        <span onclick="javascript:history.back();" class="buttom">VOLVER</span>
                        <?php
                    }
                    ?>
                </div>
            </td>
        </tr>
    </table>
    <?php
}
?>

<?php
/*
 * *******carga de mensajes con archivos y parametros ***********
 */

function sendFileConvinated() {

    title("Env&iacute;o de SMS", "Combinado.", "send");
    $login = $_SESSION["user"];
    $company = $_SESSION["id"];
    $max_char = '160';
    ?>
    <form action="?seccion=sendFileConvinatedValue" method="POST" name="tmc" enctype="multipart/form-data">
        <table width="100%" border="0" cellpadding="0" cellspacing="2" class="table-general">
            <tr>
                <td align="left" valign="top"><b>Mensaje</b></td>
                <td align="left">
                    <?php
                    $msg_example = "Escriba un mensaje agregando paramteros:\nEjemplo:\n\nEstimado Sr: ==P1==\nLe informamos que tiene ==P2== de credito para realizar compras hasta el ==P3==";
                    ?>
                    <textarea id="msgtext" name="msgtext" cols="50" rows="7"   wrap="hard" 
                              placeholder="<?php echo $msg_example; ?>"
                              onKeyDown="contador(this.form.msgtext, this.form.remLen,<?= $max_char ?>);" 
                              onKeyUp="contador(this.form.msgtext, this.form.remLen,<?= $max_char ?>);"
                              ></textarea>
                </td>
            </tr>
            <tr>
                <td align="left" valign="top" height="30"><b>Formato requerido del Archivo</b></td>
                <td align="left" valign="top">
                    <ul>
                        <li>* El tipo de archivo debe ser <b>TXT</b> o <b>CSV</b></li>
                        <li>* Los campos deben ser Celular;Fecha;PARAMETRO1;PARAMETRO2;...</li>
                        <li>* La fecha s&oacute;lo se ingresa en caso de ser un env&iacute;o agendado</li>
                        <li>* El n&uacute;mero m&aacute;ximo de Par&aacute;metros es 9</li>
                    </ul>
                </td>
            </tr>
            <tr>
                <td align="left" valign="top" height="30"><b>Archivo a subir</b></td>
                <td align="left" valign="top">
                    <table>
                        <tr>
                            <td><input id="filetxt" name="filetxt" type="file" ></td>
                            <td>
                                <button id="btnSend" type="button" onClick="sendFileConvinated();">Cargar</button>
                                <button id="btnLimpiar" type="button" onClick="redirigir('mcs.php?seccion=sendFileConvinated');">Limpiar</button>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>
    </form>


    <div id="resultado"></div>
    <?php
}
?>
