<?php
	include('config.php');

	// Conexión MySQLi global para compatibilidad con código legacy
	$mysqli_connection = mysqli_connect(SERVERDB, USERDB, PASSDB, DB);
	if (!$mysqli_connection) {
		die("Error de conexión MySQLi: " . mysqli_connect_error());
	}
	mysqli_set_charset($mysqli_connection, 'utf8');

	// Conexión PDO (recomendada para nuevo código)
	try {
		$dbh = new PDO("mysql:dbname=".DB.";host=".SERVERDB, USERDB, PASSDB);
		$dbh->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
		$dbh->exec("set names utf8");
	} catch (PDOException $e) {
		die("Error de conexión PDO: " . $e->getMessage());
	}

	// Incluir funciones de compatibilidad para mysql_*
	include_once('includes/php/mysql_compatibility.php');

	if (!isset($_SESSION)) { session_start(); }
?>
