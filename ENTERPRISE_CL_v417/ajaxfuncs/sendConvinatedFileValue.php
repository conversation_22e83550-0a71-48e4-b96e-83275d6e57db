<?php
include('../dbConn.php');
include('../includes/php/general_funcs.php');
include ('../includes/php/general_function_control.php');
include ('../includes/php/general_function_return.php');
include ('../includes/php/general_function_page.php');

$val = "...";
if (!is_dir("../loadFiles/")) {
	mkdir("../loadFiles/", 0777);
}
$msgtext = obtener_parameter("msgtext", "POST");
$arrEncontrados = "";
$countParameters = 0;
if (!empty($msgtext)) {
	$arrEncontrados = obtenerParametros($msgtext);
	$countParameters = count($arrEncontrados);
}
$max_char = return_max_char();
$filename = $_FILES['filetxt']['name'];
$path = "../loadFiles/";
$fileType = pathinfo($filename);
$format = "d-m-Y H:i:s";
$format_date_file = "dmY_His";
$maximumRecordsPerInsert = 100;
if (defined("MAX_STACK_MSG_UPLOAD")) {
	$maximumRecordsPerInsert = MAX_STACK_MSG_UPLOAD;
}
$date_now = return_date_now_format("Y-m-d H:i:s");
$name_file = str_replace(" ", "_", substr($filename, 0, -4));
$date_local_file = return_date_now_format($format_date_file);
$file_extension = $fileType['extension'];
$fecha_actual = return_date_now_format($format);
$misdn =   $_SESSION["PLACEHOLDER_NUMBER_MOBILE"];
$lengh =   $_SESSION["SIZE_NUMBER_MOBILE"] ;
$codePais =  $_SESSION["REX_TYPE_NUMBER_MOBILE"];

//	$id_file_tmp = insertTempFile($name_file, $fecha_actual);
$id_process = add_process_uploadfile("UPLOAD", $name_file, 0, $date_now);
$filename_tmp = $name_file . "_id_" . $id_process . "_" . $date_local_file . "." . $file_extension;

$max_row_file = 0;
if (defined("MAX_ROW_MSG_UPLOAD")) {
	$max_row_file = MAX_ROW_MSG_UPLOAD;
}
$max_characters = 0;
if (defined("MAX_LENGTH_TEXT_MSG")) {
	$max_characters_msg = MAX_LENGTH_TEXT_MSG;
}

$length_movil = 0;
if ($lengh) {
	$length_movil = $lengh;
}

$format_mobil = "";
if ($misdn) {
	$format_mobil = $misdn;
}

$txt_validate_mobil = "";
if ($codePais) {
	$txt_validate_mobil = $codePais;
}

$cont_errores = 0;
$data_insert = "";
$end_insert = 1;
$start_insert = 1;
$id = 0;
$error_msg = "";
?>
<table width="700" class="table-general" >
	<tr>
		<td >
			<div class="alert_big">
				<?php
				if ($fileType['extension'] == "txt" || $fileType['extension'] == "csv") {
					if (move_uploaded_file($_FILES['filetxt']['tmp_name'], $path . $filename_tmp)) {
//						if (move_uploaded_file($_FILES['filetxt']['tmp_name'], $filename_tmp)) {
						$arr = "";
						$encodingFile = "";
						$cmd_file_linux = 'file -bi ' . $path . $filename_tmp;
						$retorno_cmd_linux = shell_exec($cmd_file_linux);
						$arr = explode(";", $retorno_cmd_linux);
						$arr = explode("=", $arr[1]);
						$encodingFile = trim($arr[1]);
						if ($encodingFile != 'utf-8') {
							$mv_inut = $path . $filename_tmp;
							$mv_output = $path . $encodingFile . '_' . $filename_tmp;
							$cmd_mv_linux = "mv $mv_inut $mv_output";
							shell_exec($cmd_mv_linux);

							$file_output = $path . $filename_tmp;
							$file_input = $path . $encodingFile . '_' . $filename_tmp;
							$cmd_iconv_linux = "iconv -t utf-8 -f $encodingFile -o $file_output $file_input";
							shell_exec($cmd_iconv_linux);
						}

						$lines = file($path . $filename_tmp);
						$lengh_array = count($lines);
						if ($lengh_array <= $max_row_file) {
							// revision de linea 1
							$line = $lines[0];
							$datos = explode(";", $line);
							$cant_colum = count($datos);
							//parametros
							$columnasArchivo = "Celular, fecha";
							for ($i = 0; $i < $countParameters; $i++) {
								$columnasArchivo .= ", " . $arrEncontrados[$i];
							}

							if ($cant_colum == ($countParameters + 2)) {
								$msgtextAux = $msgtext;
								for ($i = 0; $i < $countParameters; $i++) {
									$parameter = $arrEncontrados[$i];
									$valor = $datos[$i + 2];
									$msgtextAux = str_replace($parameter, $valor, $msgtextAux);
								}
								$msg_preview = trim($msgtextAux);
								$phone_preview = trim($datos[0]);
								if ($id_process > 0) {
									$status = "PROCESSING";
									updateProcessFile($filename_tmp, $id_process, $lengh_array, "", $status); //esto para editar el nombre del archivo con el id de carga
									//iteracion de las lineas del archivo
									for ($index = 0; $index < $lengh_array; $index++) {
										$carrier = "UNKNOWN";
										$line = quitarEnter($lines[$index]);
										if (strlen($line) > 0) {
											$i = $index + 1;
											//se obtienen los datos de la linea del archivo
											$datos = explode(";", $line);
											$msgtextAux = $msgtext;
											for ($i = 0; $i < $countParameters; $i++) {
												$parameter = $arrEncontrados[$i];
												$valor = $datos[$i + 2];
												$msgtextAux = str_replace($parameter, $valor, $msgtextAux);
											}
											$phone = trim($datos[0]);
											$date = trim($datos[1]);
											$msg = trim($msgtextAux);
											//validacion de numero mobil
											if (validate_format_phone($phone, $txt_validate_mobil)) {
												$carrier = return_carrier($phone);
											} else {
												$error_msg = "El numero no cumple con el formato requerido de $length_movil digitos ($format_mobil)";
												echo "<b>Error Line $i:</b> El n&uacute;mero no cumple con el formato requerido de $length_movil digitos ($format_mobil)<br>";
												$cont_errores += 1;
											}
											if ($date == null || empty($date) || $date == "undefined") {
												$date = $fecha_actual;
											}
											if (validate_format_datetime($date)) {
												$vdate = return_date_after_format($date, $format);
												if (strtotime($vdate) >= strtotime($fecha_actual)) {
													if (!validate_time_available_send_message($date)) {
														$error_msg = "La fecha $date se encuentra fuera de los rangos de disponibilidad del servicio.";
														$msgdate = "<b>Error Line $i:</b> La fecha <b>$date</b> se encuentra fuera de los rangos de disponibilidad del servicio.<br>";
														echo $msgdate;
														$cont_errores += 1;
													}
												} else {
													$error_msg = "La fecha/hora de $date no puede ser menor a la actual";
													echo "<b>Error Line $i:</b> La fecha/hora de <b>$date</b> no puede ser menor a la actual<br>";
													$cont_errores += 1;
												}
											} else {
												$error_msg = "La fecha $date no coincide con el formato dd-mm-yyyy HH:mm:ss reuerida para la carga agendada";
												echo "<b>Error Line $i:</b> La fecha <b>$date</b> no coincide con el formato <b>dd-mm-yyyy HH:mm:ss</b> reuerida para la carga agendada<br>";
												$cont_errores += 1;
											}

											//validacion de mensaje
											if (empty($msg)) {
												$error_msg = "Mensaje Vacio";
												echo "<b>Error Line $i:</b> Mensaje Vacio <br>";
												$cont_errores += 1;
											}
											if (strlen($msg) > $max_characters_msg) {
												$error_msg = "El largo del mensaje no debe ser superior a los $max_characters_msg caracteres";
												echo "<b>Error Line $i:</b> El largo del mensaje no debe ser superior a los $max_characters_msg caracteres";
												$cont_errores += 1;
											}
											$date_gmt = return_date_after_format_gmt($date, $format);
											if (empty($data_insert)) {
												$data_insert = "($id_process, $phone, '$date', '$date_gmt', '$msg', '$carrier', 'PENDING' )";
											} else {
												$data_insert .= ", ($id_process, $phone, '$date', '$date_gmt', '$msg', '$carrier', 'PENDING' )";
											}
											$fila = $i;
											$end_insert = $fila;
											if ($fila % $maximumRecordsPerInsert == 0) {
												$id = 0;
												/* insertamos en tabla temporal */
												$id = insertDetailTableTemporal($data_insert);
												if ($id <= 0) {
													$error_msg = "Error al insertar las lineas de la $start_insert a la $end_insert";
													echo "Error al insertar las lineas de la <b>$start_insert:</b> a la <b>$end_insert:</b>";
													$cont_errores += 1;
												}
												$data_insert = "";
												$start_insert = $end_insert + 1;
											}
										}
									}
									$id = 0;
									$id = insertDetailTableTemporal($data_insert);
									if ($id <= 0) {
										$error_msg = "Error al insertar las lineas de la $start_insert a la $end_insert";
										echo "Error al insertar las lineas de la <b>$start_insert:</b> a la <b>$end_insert:</b>";
										$cont_errores += 1;
									}

									if ($cont_errores == 0) {
										?>
										<div>
											<table>
												<tr>
													<td><b>Archivo Cargado:</b></td>
													<td><?= $filename_tmp ?></td>
												</tr>
												<tr>
													<td><b>Previsualizaci&oacute;n Mensaje:</b></td>
													<td><?= $msg_preview; ?></td>
												</tr>
												<tr>
													<td><b>Previsualizaci&oacute;n N&uacute;mero de env&iacute;o</b></td>
													<td><?= $phone_preview; ?></td>
												</tr>
											</table>
											<form name="tmc" action="">
												<input id="hddfiletxt" name="filetxt" type="hidden" value="<?= $filename_tmp ?>">
												<input id="hddidfile" name="idfile" type="hidden" value="<?= $id_process ?>">
												<input id="hdddatelocal" name="datelocal" type="hidden" value="<?= $date_now ?>">
												<span id="btnSendFile" onclick="sendConvinatedFile();" class="buttom">ENVIAR</span>
												<div id="divloadImgMt"></div>
											</form>
										</div>
										<?php
									} else {
										$status = "FAILED";
										updateProcessFile($filename_tmp, $id_process, $lengh_array, $error_msg, $status);
										$result = updateDetailProcessToProcessed($id_process, $status);
										$ruta = getcwd();
					    $cmd_rm_linux = "rm $ruta/$path*$id_process*";
					    shell_exec($cmd_rm_linux);
										?>
										<br />
										<img src="images/icons/error.png"/>
										<b>Favor validar el archivo y volver a cargar.</b><br><br>
										<?php
									}
								} else {
									$status = "FAILED";
									updateProcessFile($filename_tmp, $id_process, 0, $error_msg, $status); // se actualiza el mensaje a este nivel ya que antes no existe el proceso ni el registro en la base de datos
									$result = updateDetailProcessToProcessed($id_process, $status);
									$ruta = getcwd();
					    $cmd_rm_linux = "rm $ruta/$path*$id_process*";
					    shell_exec($cmd_rm_linux);
									?>
									<img src="images/icons/error.png"/>
									<b>Error:</b> Error al insertar el archivo temporal.<br><br>
									<?php
								}
							} else {
								$status = "FAILED";
								updateProcessFile($filename_tmp, $id_process, 0, $error_msg, $status); // se actualiza el mensaje a este nivel ya que antes no existe el proceso ni el registro en la base de datos
								$result = updateDetailProcessToProcessed($id_process, $status);
								$ruta = getcwd();
					    $cmd_rm_linux = "rm $ruta/$path*$id_process*";
					    shell_exec($cmd_rm_linux);
								?>
								<img src="images/icons/error.png"/>
								<b>Error:</b> El archivo no cumple el formato establecido <b><?= $columnasArchivo; ?></b> Favor verificar.<br><br>
								<?php
							}
						} else {
							$status = "FAILED";
							updateProcessFile($filename_tmp, $id_process, 0, $error_msg, $status); // se actualiza el mensaje a este nivel ya que antes no existe el proceso ni el registro en la base de datos
							$result = updateDetailProcessToProcessed($id_process, $status);
							$ruta = getcwd();
					    $cmd_rm_linux = "rm $ruta/$path*$id_process*";
					    shell_exec($cmd_rm_linux);
							?>
							<img src="images/icons/error.png"/>
							<b>Error:</b> El archivo excede el m&aacute;ximo de <?= $max_characters_msg ?> lineas.<br><br>
							<?php
						}
					} else {
						$status = "FAILED";
						updateProcessFile($filename_tmp, $id_process, 0, $error_msg, $status); // se actualiza el mensaje a este nivel ya que antes no existe el proceso ni el registro en la base de datos
						$result = updateDetailProcessToProcessed($id_process, $status);
						$ruta = getcwd();
					    $cmd_rm_linux = "rm $ruta/$path*$id_process*";
					    shell_exec($cmd_rm_linux);
						?>
						<img src="images/icons/error.png"/>
						<b>Error:</b>Ocurri&oacute; alg&uacute;n error al subir el fichero.
						<?php
					}
				} else {
					$status = "FAILED";
					updateProcessFile($filename_tmp, $id_process, 0, $error_msg, $status); // se actualiza el mensaje a este nivel ya que antes no existe el proceso ni el registro en la base de datos
					$result = updateDetailProcessToProcessed($id_process, $status);
					$ruta = getcwd();
					    $cmd_rm_linux = "rm $ruta/$path*$id_process*";
					    shell_exec($cmd_rm_linux);
					?>
					<img src="images/icons/error.png"/>
					<b>Error:</b> El archivo debe ser <b>.txt</b> o <b>.csv</b>. Favor verificar y volver a cargar <a href='?seccion=sendFile'><b>aquí</b></a>.
					<?php
				}
				?>
			</div>
		</td>
	</tr>
</table>